import 'package:flutter/material.dart' as material;
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/astrology/event_detection_config.dart';
import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/event_timeline_data.dart';
import '../../../data/models/user/birth_data.dart';
import '../../../features/astrology/astrology_service.dart';
import '../../../features/astrology/services/event_detection_service.dart';
import '../../../presentation/themes/app_theme.dart';
import '../../widgets/astrology/astro_event_calendar_widget.dart';
import '../../widgets/astrology/event_detail_panel.dart';
import '../../widgets/astrology/event_detection_settings_dialog.dart';
import '../../widgets/astrology/event_timeline_widget.dart';
import '../../widgets/common/styled_card.dart';

/// 占星事件偵測主頁面
///
/// 整合年曆熱度圖、時間軸圖表和事件詳情面板
class AstroEventDetectionPage extends StatefulWidget {
  /// 出生資料
  final BirthData birthData;

  const AstroEventDetectionPage({
    super.key,
    required this.birthData,
  });

  @override
  State<AstroEventDetectionPage> createState() =>
      _AstroEventDetectionPageState();
}

class _AstroEventDetectionPageState extends State<AstroEventDetectionPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  EventTimelineData? _timelineData;
  DateTime? _selectedDate;
  DailyEventScore? _selectedDayScore;
  List<AstroEvent> _selectedDayEvents = [];

  bool _isLoading = false;
  String? _errorMessage;

  late EventDetectionService _eventDetectionService;

  // 日期範圍
  late DateTime _startDate;
  late DateTime _endDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 設定預設日期範圍（當前年份）
    final now = DateTime.now();
    // _startDate = DateTime(now.year, 1, 1);
    // _endDate = DateTime(now.year, 12, 31);
    _startDate = DateTime(now.year, now.month, 1); // 當月第一天
    _endDate = DateTime(now.year, now.month + 1, 0); // 當月最後一天

    // 初始化事件偵測服務
    _eventDetectionService = EventDetectionService(
      config: EventDetectionConfig.defaultConfig(),
      astrologyService: AstrologyService(),
    );

    // 開始載入事件資料
    _loadEventData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String>(
      future: _getUserMode(),
      builder: (context, snapshot) {
        final userMode = snapshot.data ?? 'starmaster';
        final pageType = userMode == 'starmaster'
            ? PageType.starmaster
            : PageType.starlight;

        return Theme(
          data: ThemeManager.getThemeForPage(pageType),
          child: Scaffold(
            appBar: _buildAppBar(pageType),
            body: _buildBody(pageType),
          ),
        );
      },
    );
  }

  /// 獲取用戶模式
  Future<String> _getUserMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('user_mode') ?? 'starmaster';
    } catch (e) {
      return 'starmaster';
    }
  }

  /// 建立應用欄
  PreferredSizeWidget _buildAppBar(PageType pageType) {
    final primaryColor = ThemeManager.getPrimaryColorForPage(pageType);

    return AppBar(
      title: const Text('占星事件偵測'),
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: _showSettings,
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isLoading ? null : _loadEventData,
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: const [
          Tab(
            icon: Icon(Icons.calendar_view_month),
            text: '年曆熱度圖',
          ),
          Tab(
            icon: Icon(Icons.timeline),
            text: '時間軸圖表',
          ),
        ],
      ),
    );
  }

  /// 建立主體內容
  Widget _buildBody(PageType pageType) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_timelineData == null) {
      return _buildEmptyState();
    }

    return Stack(
      children: [
        Column(
          children: [
            _buildDateRangeSelector(pageType),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildCalendarView(pageType),
                  _buildTimelineView(pageType),
                ],
              ),
            ),
          ],
        ),
        if (_selectedDate != null) _buildEventDetailPanel(pageType),
      ],
    );
  }

  /// 建立載入狀態
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('正在分析占星事件...'),
        ],
      ),
    );
  }

  /// 建立錯誤狀態
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            '載入事件資料時發生錯誤',
            style: Theme
                .of(context)
                .textTheme
                .titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? '未知錯誤',
            style: Theme
                .of(context)
                .textTheme
                .bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadEventData,
            child: const Text('重新載入'),
          ),
        ],
      ),
    );
  }

  /// 建立空狀態
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text('沒有找到事件資料'),
        ],
      ),
    );
  }

  /// 建立日期範圍選擇器
  Widget _buildDateRangeSelector(PageType pageType) {
    return StyledCard(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.date_range),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '分析期間: ${_startDate.year}/${_startDate.month}/${_startDate
                    .day} - ${_endDate.year}/${_endDate.month}/${_endDate.day}',
                style: const TextStyle(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            TextButton(
              onPressed: _selectDateRange,
              child: const Text('更改'),
            ),
          ],
        ),
      ),
    );
  }

  /// 建立日曆視圖
  Widget _buildCalendarView(PageType pageType) {
    final theme = pageType == PageType.starmaster
        ? EventCalendarTheme.starmaster()
        : EventCalendarTheme.starlight();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: AstroEventCalendarWidget(
        timelineData: _timelineData!,
        theme: theme,
        onDayTapped: _handleDayTapped,
      ),
    );
  }

  /// 建立時間軸視圖
  Widget _buildTimelineView(PageType pageType) {
    final theme = pageType == PageType.starmaster
        ? const EventTimelineTheme.starmaster()
        : const EventTimelineTheme.starlight();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: EventTimelineWidget(
        timelineData: _timelineData!,
        theme: theme,
        onDataPointTapped: _handleDayTapped,
      ),
    );
  }

  /// 建立事件詳情面板
  Widget _buildEventDetailPanel(PageType pageType) {
    final theme = pageType == PageType.starmaster
        ? const EventDetailTheme.starmaster()
        : const EventDetailTheme.starlight();

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: EventDetailPanel(
        selectedDate: _selectedDate!,
        dailyScore: _selectedDayScore,
        events: _selectedDayEvents,
        theme: theme,
        onClose: () {
          setState(() {
            _selectedDate = null;
            _selectedDayScore = null;
            _selectedDayEvents = [];
          });
        },
      ),
    );
  }

  /// 載入事件資料
  Future<void> _loadEventData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final timelineData = await _eventDetectionService.detectEvents(
        widget.birthData,
        _startDate,
        _endDate,
      );

      setState(() {
        _timelineData = timelineData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  /// 處理日期點擊
  void _handleDayTapped(DateTime date, DailyEventScore? score) {
    final events = _timelineData?.getEventsInRange(
      date,
      date.add(const Duration(days: 1)),
    ) ?? [];

    setState(() {
      _selectedDate = date;
      _selectedDayScore = score;
      _selectedDayEvents = events;
    });
  }

  /// 選擇日期範圍
  Future<void> _selectDateRange() async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(DateTime
          .now()
          .year - 5),
      lastDate: DateTime(DateTime
          .now()
          .year + 5),
      initialDateRange: material.DateTimeRange(start: _startDate, end: _endDate),
    );

    if (dateRange != null) {
      setState(() {
        _startDate = dateRange.start;
        _endDate = dateRange.end;
      });

      _loadEventData();
    }
  }

  /// 顯示設定
  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => EventDetectionSettingsDialog(
        birthData: widget.birthData,
        onSettingsChanged: () {
          // 設定變更後重新載入資料
          _loadEventData();
        },
      ),
    );
  }
}
