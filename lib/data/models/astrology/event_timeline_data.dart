import 'astro_event.dart';
import 'event_score.dart';

/// 事件時間軸資料模型
class EventTimelineData {
  /// 時間軸開始日期
  final DateTime startDate;
  
  /// 時間軸結束日期
  final DateTime endDate;
  
  /// 每日事件評分列表
  final List<DailyEventScore> dailyScores;
  
  /// 所有事件列表
  final List<AstroEvent> allEvents;
  
  /// 最高分數
  final double maxScore;
  
  /// 最低分數
  final double minScore;
  
  /// 平均分數
  final double averageScore;
  
  /// 總事件數量
  final int totalEventCount;

  const EventTimelineData({
    required this.startDate,
    required this.endDate,
    required this.dailyScores,
    required this.allEvents,
    required this.maxScore,
    required this.minScore,
    required this.averageScore,
    required this.totalEventCount,
  });

  /// 從每日評分列表創建時間軸資料
  factory EventTimelineData.fromDailyScores(
    DateTime startDate,
    DateTime endDate,
    List<DailyEventScore> dailyScores,
  ) {
    final allEvents = <AstroEvent>[];
    double totalScore = 0;
    double maxScore = 0;
    double minScore = double.infinity;
    
    for (final dailyScore in dailyScores) {
      totalScore += dailyScore.totalScore;
      if (dailyScore.totalScore > maxScore) {
        maxScore = dailyScore.totalScore;
      }
      if (dailyScore.totalScore < minScore && dailyScore.totalScore > 0) {
        minScore = dailyScore.totalScore;
      }
    }
    
    if (minScore == double.infinity) minScore = 0.0;
    
    final averageScore = dailyScores.isNotEmpty ? totalScore / dailyScores.length : 0.0;
    final totalEventCount = allEvents.length;

    return EventTimelineData(
      startDate: startDate,
      endDate: endDate,
      dailyScores: dailyScores,
      allEvents: allEvents,
      maxScore: maxScore,
      minScore: minScore,
      averageScore: averageScore,
      totalEventCount: totalEventCount,
    );
  }

  /// 獲取指定日期的事件評分
  DailyEventScore? getScoreForDate(DateTime date) {
    try {
      return dailyScores.firstWhere(
        (score) => _isSameDay(score.date, date),
      );
    } catch (e) {
      return null;
    }
  }

  /// 獲取指定日期範圍的事件
  List<AstroEvent> getEventsInRange(DateTime start, DateTime end) {
    return allEvents.where((event) {
      return event.dateTime.isAfter(start.subtract(const Duration(days: 1))) &&
             event.dateTime.isBefore(end.add(const Duration(days: 1)));
    }).toList();
  }

  /// 獲取高分事件（分數超過平均值的事件）
  List<DailyEventScore> getHighScoreDays({double? threshold}) {
    final scoreThreshold = threshold ?? averageScore;
    return dailyScores.where((score) => score.totalScore >= scoreThreshold).toList();
  }

  /// 獲取低分事件（分數低於平均值的事件）
  List<DailyEventScore> getLowScoreDays({double? threshold}) {
    final scoreThreshold = threshold ?? averageScore;
    return dailyScores.where((score) => score.totalScore < scoreThreshold).toList();
  }

  /// 獲取指定事件類型的統計
  Map<AstroEventType, int> getEventTypeStatistics() {
    final statistics = <AstroEventType, int>{};
    
    for (final event in allEvents) {
      statistics[event.type] = (statistics[event.type] ?? 0) + 1;
    }
    
    return statistics;
  }

  /// 獲取月度統計
  List<MonthlyEventSummary> getMonthlyStatistics() {
    final monthlyData = <String, List<DailyEventScore>>{};
    
    for (final dailyScore in dailyScores) {
      final monthKey = '${dailyScore.date.year}-${dailyScore.date.month.toString().padLeft(2, '0')}';
      monthlyData[monthKey] ??= [];
      monthlyData[monthKey]!.add(dailyScore);
    }
    
    return monthlyData.entries.map((entry) {
      final monthScores = entry.value;
      final totalScore = monthScores.fold<double>(0, (sum, score) => sum + score.totalScore);
      final averageScore = monthScores.isNotEmpty ? totalScore / monthScores.length : 0.0;
      final maxScore = monthScores.isNotEmpty
          ? monthScores.map((s) => s.totalScore).reduce((a, b) => a > b ? a : b)
          : 0.0;
      
      return MonthlyEventSummary(
        month: entry.key,
        totalScore: totalScore,
        averageScore: averageScore,
        maxScore: maxScore,
        dayCount: monthScores.length,
        dailyScores: monthScores,
      );
    }).toList();
  }

  /// 從 Map 創建 EventTimelineData
  factory EventTimelineData.fromMap(Map<String, dynamic> map) {
    return EventTimelineData(
      startDate: DateTime.parse(map['startDate'] as String),
      endDate: DateTime.parse(map['endDate'] as String),
      dailyScores: (map['dailyScores'] as List)
          .map((e) => DailyEventScore.fromMap(e as Map<String, dynamic>))
          .toList(),
      allEvents: (map['allEvents'] as List)
          .map((e) => AstroEvent.fromMap(e as Map<String, dynamic>))
          .toList(),
      maxScore: (map['maxScore'] as num).toDouble(),
      minScore: (map['minScore'] as num).toDouble(),
      averageScore: (map['averageScore'] as num).toDouble(),
      totalEventCount: map['totalEventCount'] as int,
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'dailyScores': dailyScores.map((e) => e.toMap()).toList(),
      'allEvents': allEvents.map((e) => e.toMap()).toList(),
      'maxScore': maxScore,
      'minScore': minScore,
      'averageScore': averageScore,
      'totalEventCount': totalEventCount,
    };
  }

  /// 複製並修改
  EventTimelineData copyWith({
    DateTime? startDate,
    DateTime? endDate,
    List<DailyEventScore>? dailyScores,
    List<AstroEvent>? allEvents,
    double? maxScore,
    double? minScore,
    double? averageScore,
    int? totalEventCount,
  }) {
    return EventTimelineData(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      dailyScores: dailyScores ?? this.dailyScores,
      allEvents: allEvents ?? this.allEvents,
      maxScore: maxScore ?? this.maxScore,
      minScore: minScore ?? this.minScore,
      averageScore: averageScore ?? this.averageScore,
      totalEventCount: totalEventCount ?? this.totalEventCount,
    );
  }

  /// 檢查兩個日期是否為同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  @override
  String toString() {
    return 'EventTimelineData(startDate: $startDate, endDate: $endDate, totalEvents: $totalEventCount)';
  }
}

/// 月度事件摘要
class MonthlyEventSummary {
  /// 月份標識 (YYYY-MM)
  final String month;
  
  /// 該月總分數
  final double totalScore;
  
  /// 該月平均分數
  final double averageScore;
  
  /// 該月最高分數
  final double maxScore;
  
  /// 該月天數
  final int dayCount;
  
  /// 該月每日評分
  final List<DailyEventScore> dailyScores;

  const MonthlyEventSummary({
    required this.month,
    required this.totalScore,
    required this.averageScore,
    required this.maxScore,
    required this.dayCount,
    required this.dailyScores,
  });

  /// 獲取月份顯示名稱
  String get displayName {
    final parts = month.split('-');
    if (parts.length == 2) {
      return '${parts[0]}年${int.parse(parts[1])}月';
    }
    return month;
  }

  /// 從 Map 創建 MonthlyEventSummary
  factory MonthlyEventSummary.fromMap(Map<String, dynamic> map) {
    return MonthlyEventSummary(
      month: map['month'] as String,
      totalScore: (map['totalScore'] as num).toDouble(),
      averageScore: (map['averageScore'] as num).toDouble(),
      maxScore: (map['maxScore'] as num).toDouble(),
      dayCount: map['dayCount'] as int,
      dailyScores: (map['dailyScores'] as List)
          .map((e) => DailyEventScore.fromMap(e as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'month': month,
      'totalScore': totalScore,
      'averageScore': averageScore,
      'maxScore': maxScore,
      'dayCount': dayCount,
      'dailyScores': dailyScores.map((e) => e.toMap()).toList(),
    };
  }

  @override
  String toString() {
    return 'MonthlyEventSummary(month: $month, averageScore: $averageScore, dayCount: $dayCount)';
  }
}
