import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/models/astrology/event_timeline_data.dart';
import '../../../data/models/user/birth_data.dart';
import '../../../shared/utils/logger_utils.dart';

/// 事件快取服務
/// 
/// 負責快取事件偵測結果，提升效能
class EventCacheService {
  static const String _cacheKeyPrefix = 'event_cache_';
  static const String _cacheVersionKey = 'event_cache_version';
  static const int _currentCacheVersion = 1;
  static const Duration _cacheExpiry = Duration(days: 7); // 快取7天

  /// 獲取快取的事件時間軸資料
  /// 
  /// [birthData] 出生資料
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// 
  /// 返回快取的資料，如果沒有或已過期則返回 null
  static Future<EventTimelineData?> getCachedEventData(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 檢查快取版本
      final cacheVersion = prefs.getInt(_cacheVersionKey) ?? 0;
      if (cacheVersion != _currentCacheVersion) {
        logger.d('快取版本不匹配，清除舊快取');
        await _clearAllCache();
        return null;
      }
      
      final cacheKey = _generateCacheKey(birthData, startDate, endDate);
      final cachedJson = prefs.getString(cacheKey);
      
      if (cachedJson == null) {
        logger.d('沒有找到快取資料: $cacheKey');
        return null;
      }
      
      final cachedData = jsonDecode(cachedJson) as Map<String, dynamic>;
      final cacheTime = DateTime.parse(cachedData['cacheTime'] as String);
      
      // 檢查是否過期
      if (DateTime.now().difference(cacheTime) > _cacheExpiry) {
        logger.d('快取已過期，刪除: $cacheKey');
        await prefs.remove(cacheKey);
        return null;
      }
      
      logger.d('找到有效快取資料: $cacheKey');
      return EventTimelineData.fromMap(cachedData['data'] as Map<String, dynamic>);
    } catch (e) {
      logger.e('讀取快取時出錯: $e');
      return null;
    }
  }

  /// 快取事件時間軸資料
  /// 
  /// [birthData] 出生資料
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// [timelineData] 要快取的資料
  static Future<void> cacheEventData(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate,
    EventTimelineData timelineData,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 設定快取版本
      await prefs.setInt(_cacheVersionKey, _currentCacheVersion);
      
      final cacheKey = _generateCacheKey(birthData, startDate, endDate);
      final cacheData = {
        'cacheTime': DateTime.now().toIso8601String(),
        'data': timelineData.toMap(),
      };
      
      await prefs.setString(cacheKey, jsonEncode(cacheData));
      logger.d('快取資料已保存: $cacheKey');
      
      // 清理過期快取
      await _cleanupExpiredCache();
    } catch (e) {
      logger.e('保存快取時出錯: $e');
    }
  }

  /// 清除指定出生資料的所有快取
  /// 
  /// [birthData] 出生資料
  static Future<void> clearCacheForBirthData(BirthData birthData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final birthDataHash = _generateBirthDataHash(birthData);
      
      final keysToRemove = keys.where((key) => 
        key.startsWith(_cacheKeyPrefix) && key.contains(birthDataHash)
      ).toList();
      
      for (final key in keysToRemove) {
        await prefs.remove(key);
      }
      
      logger.d('已清除 ${keysToRemove.length} 個快取項目');
    } catch (e) {
      logger.e('清除快取時出錯: $e');
    }
  }

  /// 清除所有快取
  static Future<void> clearAllCache() async {
    await _clearAllCache();
  }

  /// 獲取快取統計資訊
  static Future<Map<String, dynamic>> getCacheStatistics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      final cacheKeys = keys.where((key) => key.startsWith(_cacheKeyPrefix)).toList();
      int totalSize = 0;
      int expiredCount = 0;
      
      for (final key in cacheKeys) {
        final cachedJson = prefs.getString(key);
        if (cachedJson != null) {
          totalSize += cachedJson.length;
          
          try {
            final cachedData = jsonDecode(cachedJson) as Map<String, dynamic>;
            final cacheTime = DateTime.parse(cachedData['cacheTime'] as String);
            
            if (DateTime.now().difference(cacheTime) > _cacheExpiry) {
              expiredCount++;
            }
          } catch (e) {
            expiredCount++;
          }
        }
      }
      
      return {
        'totalItems': cacheKeys.length,
        'totalSizeBytes': totalSize,
        'expiredItems': expiredCount,
        'validItems': cacheKeys.length - expiredCount,
      };
    } catch (e) {
      logger.e('獲取快取統計時出錯: $e');
      return {
        'totalItems': 0,
        'totalSizeBytes': 0,
        'expiredItems': 0,
        'validItems': 0,
      };
    }
  }

  /// 生成快取鍵
  static String _generateCacheKey(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate,
  ) {
    final birthDataHash = _generateBirthDataHash(birthData);
    final dateRange = '${startDate.toIso8601String().substring(0, 10)}_${endDate.toIso8601String().substring(0, 10)}';
    return '${_cacheKeyPrefix}${birthDataHash}_$dateRange';
  }

  /// 生成出生資料雜湊
  static String _generateBirthDataHash(BirthData birthData) {
    final key = '${birthData.dateTime.toIso8601String()}_${birthData.latitude}_${birthData.longitude}';
    return key.hashCode.abs().toString();
  }

  /// 清理過期快取
  static Future<void> _cleanupExpiredCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final cacheKeys = keys.where((key) => key.startsWith(_cacheKeyPrefix)).toList();
      
      int removedCount = 0;
      
      for (final key in cacheKeys) {
        final cachedJson = prefs.getString(key);
        if (cachedJson != null) {
          try {
            final cachedData = jsonDecode(cachedJson) as Map<String, dynamic>;
            final cacheTime = DateTime.parse(cachedData['cacheTime'] as String);
            
            if (DateTime.now().difference(cacheTime) > _cacheExpiry) {
              await prefs.remove(key);
              removedCount++;
            }
          } catch (e) {
            // 如果解析失敗，也刪除這個快取項目
            await prefs.remove(key);
            removedCount++;
          }
        }
      }
      
      if (removedCount > 0) {
        logger.d('清理了 $removedCount 個過期快取項目');
      }
    } catch (e) {
      logger.e('清理過期快取時出錯: $e');
    }
  }

  /// 清除所有快取（內部方法）
  static Future<void> _clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final cacheKeys = keys.where((key) => 
        key.startsWith(_cacheKeyPrefix) || key == _cacheVersionKey
      ).toList();
      
      for (final key in cacheKeys) {
        await prefs.remove(key);
      }
      
      logger.d('已清除所有快取，共 ${cacheKeys.length} 個項目');
    } catch (e) {
      logger.e('清除所有快取時出錯: $e');
    }
  }
}

/// 快取管理器
/// 
/// 提供更高層級的快取管理功能
class EventCacheManager {
  /// 預熱快取
  /// 
  /// 為常用的日期範圍預先計算和快取資料
  static Future<void> preloadCache(
    BirthData birthData,
    List<DateTimeRange> dateRanges,
  ) async {
    // TODO: 實作預熱快取邏輯
    logger.d('預熱快取功能待實作');
  }

  /// 智能快取清理
  /// 
  /// 根據使用頻率和重要性智能清理快取
  static Future<void> smartCleanup() async {
    // TODO: 實作智能清理邏輯
    logger.d('智能清理功能待實作');
  }

  /// 快取壓縮
  /// 
  /// 壓縮快取資料以節省空間
  static Future<void> compressCache() async {
    // TODO: 實作快取壓縮邏輯
    logger.d('快取壓縮功能待實作');
  }
}

/// 日期時間範圍
class DateTimeRange {
  final DateTime start;
  final DateTime end;

  const DateTimeRange({
    required this.start,
    required this.end,
  });

  @override
  String toString() {
    return 'DateTimeRange(start: $start, end: $end)';
  }
}
