import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../core/utils/logger_utils.dart';
import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../../data/models/astrology/event_detection_config.dart';
import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/event_timeline_data.dart';
import '../../../data/models/user/birth_data.dart';
import '../astrology_service.dart';
import '../calculations/event_score_calculator.dart';
import 'event_cache_service.dart';
import 'life_events_detector.dart';

/// 事件偵測服務
/// 
/// 負責偵測和分析占星事件，包括：
/// - 行運事件偵測
/// - 推運事件偵測
/// - 事件評分計算
/// - 時間軸資料生成
class EventDetectionService {
  /// 事件偵測配置
  final EventDetectionConfig config;
  
  /// 事件評分計算器
  late final EventScoreCalculator _scoreCalculator;

  /// 占星服務
  late final AstrologyService _astrologyService;
  
  /// 占星服務
  final AstrologyService _astrologyService;

  EventDetectionService({
    required this.config,
    required AstrologyService astrologyService,
  }) : _astrologyService = astrologyService {
    _scoreCalculator = EventScoreCalculator(
      config: EventScoreConfig.defaultConfig(),
    );
  }

  /// 偵測指定時間範圍內的所有事件
  ///
  /// [birthData] 出生資料
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// [useCache] 是否使用快取
  ///
  /// 返回事件時間軸資料
  Future<EventTimelineData> detectEvents(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate, {
    bool useCache = true,
  }) async {
    logger.d('開始偵測事件: ${startDate.toString()} 到 ${endDate.toString()}');

    // 嘗試從快取載入
    if (useCache) {
      final cachedData = await EventCacheService.getCachedEventData(
        birthData,
        startDate,
        endDate,
      );

      if (cachedData != null) {
        logger.d('使用快取資料');
        return cachedData;
      }
    }

    final dailyScores = <DailyEventScore>[];
    final allEvents = <AstroEvent>[];

    // 逐日偵測事件
    DateTime currentDate = startDate;
    while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
      try {
        final dayEvents = await _detectDailyEvents(birthData, currentDate);
        final dayScores = dayEvents.map((event) =>
          _scoreCalculator.calculateAspectScore(
            event.additionalData?['aspect'],
            birthData,
            isTransit: event.type == AstroEventType.transitAspect,
            isPrecise: event.isExact ?? false,
          )
        ).toList();

        final dailyScore = DailyEventScore.fromEvents(currentDate, dayScores);
        dailyScores.add(dailyScore);
        allEvents.addAll(dayEvents);

        logger.d('${currentDate.toString().substring(0, 10)}: ${dayEvents.length} 個事件, 總分: ${dailyScore.totalScore.toStringAsFixed(1)}');
      } catch (e) {
        logger.e('偵測 $currentDate 事件時出錯: $e');
        // 添加空的每日評分以保持連續性
        dailyScores.add(DailyEventScore(
          date: currentDate,
          totalScore: 0,
          events: [],
          eventCount: 0,
        ));
      }

      currentDate = currentDate.add(const Duration(days: 1));
    }

    final timelineData = EventTimelineData.fromDailyScores(
      startDate,
      endDate,
      dailyScores,
    );

    // 快取結果
    if (useCache) {
      await EventCacheService.cacheEventData(
        birthData,
        startDate,
        endDate,
        timelineData,
      );
    }

    logger.d('事件偵測完成: 總共 ${allEvents.length} 個事件');
    return timelineData;
  }

  /// 偵測單日事件（增強版）
  ///
  /// 使用多重星盤分析來偵測重大事件
  /// 1. 先分析本命盤判斷重大事件的機率
  /// 2. 再用推運相關的盤判斷重大事件發生的時間
  Future<List<AstroEvent>> _detectDailyEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 第一階段：計算各種星盤資料
      final chartDataMap = await _calculateMultipleCharts(birthData, date);

      // 第二階段：本命盤基礎分析 - 判斷重大事件機率
      final natalAnalysis = await _analyzeNatalChartForEventPotential(
        chartDataMap[ChartType.natal]!,
        birthData,
      );

      // 第三階段：推運盤分析 - 判斷事件發生時間
      final progressionAnalysis = await _analyzeProgressionChartsForTiming(
        chartDataMap,
        birthData,
        date,
        natalAnalysis,
      );

      // 第四階段：綜合分析生成事件
      final detectedEvents = await _synthesizeEventsFromAnalysis(
        chartDataMap,
        natalAnalysis,
        progressionAnalysis,
        birthData,
        date,
      );

      events.addAll(detectedEvents);

      // 第五階段：傳統事件偵測（保持向後相容）
      final traditionalEvents = await _detectTraditionalEvents(birthData, date);
      events.addAll(traditionalEvents);

    } catch (e) {
      logger.e('增強事件偵測失敗，回退到傳統方法: $e');
      // 回退到原始方法
      final fallbackEvents = await _detectTraditionalEvents(birthData, date);
      events.addAll(fallbackEvents);
    }

    // 過濾低分事件
    return events.where((event) =>
      (event.score ?? 0) >= config.minimumEventScore
    ).toList();
  }

  /// 傳統事件偵測方法（原始邏輯）
  Future<List<AstroEvent>> _detectTraditionalEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];

    // 1. 偵測行運事件
    if (config.enabledEventTypes.contains(AstroEventType.transitAspect)) {
      final transitEvents = await _detectTransitEvents(birthData, date);
      events.addAll(transitEvents);
    }

    // 2. 偵測推運事件
    if (config.enabledEventTypes.contains(AstroEventType.progressionAspect)) {
      final progressionEvents = await _detectProgressionEvents(birthData, date);
      events.addAll(progressionEvents);
    }

    // 3. 偵測太陽弧推運事件
    if (config.enabledEventTypes.contains(AstroEventType.solarArcAspect)) {
      final solarArcEvents = await _detectSolarArcEvents(birthData, date);
      events.addAll(solarArcEvents);
    }

    // 4. 偵測行星換座事件
    if (config.enabledEventTypes.contains(AstroEventType.planetSignChange)) {
      final signChangeEvents = await _detectSignChangeEvents(birthData, date);
      events.addAll(signChangeEvents);
    }

    // 5. 偵測行星換宮事件
    if (config.enabledEventTypes.contains(AstroEventType.planetHouseChange)) {
      final houseChangeEvents = await _detectHouseChangeEvents(birthData, date);
      events.addAll(houseChangeEvents);
    }

    // 6. 偵測人生重大事件
    final majorLifeEvents = await _detectMajorLifeEvents(birthData, date);
    events.addAll(majorLifeEvents);

    return events;
  }

  /// 計算多重星盤資料
  Future<Map<ChartType, ChartData>> _calculateMultipleCharts(
    BirthData birthData,
    DateTime date,
  ) async {
    final chartDataMap = <ChartType, ChartData>{};

    try {
      // 1. 本命盤
      final natalChart = ChartData(
        chartType: ChartType.natal,
        primaryPerson: birthData,
        specificDate: birthData.dateTime,
      );
      chartDataMap[ChartType.natal] = await _astrologyService.calculateChartData(natalChart);

      // 2. 行運盤
      final transitChart = ChartData(
        chartType: ChartType.transit,
        primaryPerson: birthData,
        specificDate: date,
      );
      chartDataMap[ChartType.transit] = await _astrologyService.calculateChartData(transitChart);

      // 3. 次限推運盤
      final secondaryChart = ChartData(
        chartType: ChartType.secondaryProgression,
        primaryPerson: birthData,
        specificDate: date,
      );
      chartDataMap[ChartType.secondaryProgression] = await _astrologyService.calculateChartData(secondaryChart);

      // 4. 太陽弧推運盤
      final solarArcChart = ChartData(
        chartType: ChartType.solarArcDirection,
        primaryPerson: birthData,
        specificDate: date,
      );
      chartDataMap[ChartType.solarArcDirection] = await _astrologyService.calculateChartData(solarArcChart);

      logger.d('成功計算 ${chartDataMap.length} 個星盤');

    } catch (e) {
      logger.e('計算星盤資料時出錯: $e');
      rethrow;
    }

    return chartDataMap;
  }

  /// 分析本命盤的重大事件潛力
  Future<Map<String, dynamic>> _analyzeNatalChartForEventPotential(
    ChartData natalChart,
    BirthData birthData,
  ) async {
    final analysis = <String, dynamic>{};

    try {
      // 分析各個生活領域的敏感度
      analysis['relationship_sensitivity'] = _analyzeRelationshipSensitivity(natalChart);
      analysis['career_sensitivity'] = _analyzeCareerSensitivity(natalChart);
      analysis['financial_sensitivity'] = _analyzeFinancialSensitivity(natalChart);
      analysis['health_sensitivity'] = _analyzeHealthSensitivity(natalChart);
      analysis['learning_sensitivity'] = _analyzeLearningGrowthSensitivity(natalChart);
      analysis['relocation_sensitivity'] = _analyzeRelocationSensitivity(natalChart);
      analysis['spiritual_sensitivity'] = _analyzeSpiritualTransformationSensitivity(natalChart);

      // 計算整體敏感度分數
      analysis['overall_sensitivity'] = _calculateOverallSensitivity(analysis);

      logger.d('本命盤分析完成，整體敏感度: ${analysis['overall_sensitivity']}');

    } catch (e) {
      logger.e('本命盤分析失敗: $e');
      // 返回預設值
      analysis['overall_sensitivity'] = 50.0;
    }

    return analysis;
  }

  /// 分析感情與人際關係敏感度
  double _analyzeRelationshipSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    // 檢查7宮、5宮、4宮的行星配置
    final houses = natalChart.houses;
    final planets = natalChart.planets;
    final aspects = natalChart.aspects;

    // 7宮（伴侶關係）
    if (houses.isNotEmpty && houses.length > 6) {
      final house7 = houses[6]; // 第7宮（索引6）
      // 檢查7宮內的行星
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, house7)) {
          if (planet.name == '金星' || planet.name == '月亮' || planet.name == '火星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    // 檢查金星、月亮、火星的相位
    for (final aspect in aspects) {
      if (_isRelationshipPlanet(aspect.planet1) || _isRelationshipPlanet(aspect.planet2)) {
        if (_isChallengingAspect(aspect.type)) {
          sensitivity += 10.0;
        } else if (_isHarmoniousAspect(aspect.type)) {
          sensitivity += 5.0;
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析事業與工作轉折敏感度
  double _analyzeCareerSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets;
    final aspects = natalChart.aspects;

    // 10宮（事業）和6宮（工作）
    if (houses.isNotEmpty && houses.length > 9) {
      final house10 = houses[9]; // 第10宮
      final house6 = houses[5];  // 第6宮

      // 檢查重要行星在事業宮位
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, house10)) {
          if (planet.name == '太陽' || planet.name == '木星' || planet.name == '土星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
        if (_isPlanetInHouse(planet, house6)) {
          if (planet.name == '火星' || planet.name == '水星') {
            sensitivity += 10.0;
          } else {
            sensitivity += 5.0;
          }
        }
      }
    }

    // 檢查太陽、土星、木星的相位
    for (final aspect in aspects) {
      if (_isCareerPlanet(aspect.planet1) || _isCareerPlanet(aspect.planet2)) {
        if (_isChallengingAspect(aspect.type)) {
          sensitivity += 12.0;
        } else if (_isHarmoniousAspect(aspect.type)) {
          sensitivity += 6.0;
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析財務狀況敏感度
  double _analyzeFinancialSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets;

    // 2宮（個人財務）和8宮（共同財務）
    if (houses.isNotEmpty && houses.length > 7) {
      final house2 = houses[1]; // 第2宮
      final house8 = houses[7]; // 第8宮

      for (final planet in planets) {
        if (_isPlanetInHouse(planet, house2) || _isPlanetInHouse(planet, house8)) {
          if (planet.name == '金星' || planet.name == '木星' || planet.name == '土星') {
            sensitivity += 12.0;
          } else {
            sensitivity += 6.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析健康與身體狀況敏感度
  double _analyzeHealthSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets;

    // 6宮（健康）和8宮（生死）
    if (houses.isNotEmpty && houses.length > 7) {
      final house6 = houses[5]; // 第6宮
      final house8 = houses[7]; // 第8宮

      for (final planet in planets) {
        if (_isPlanetInHouse(planet, house6) || _isPlanetInHouse(planet, house8)) {
          if (planet.name == '火星' || planet.name == '土星' || planet.name == '天王星' || planet.name == '冥王星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析學習與成長敏感度
  double _analyzeLearningGrowthSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets;

    // 3宮（學習）和9宮（高等教育、哲學）
    if (houses.isNotEmpty && houses.length > 8) {
      final house3 = houses[2]; // 第3宮
      final house9 = houses[8]; // 第9宮

      for (final planet in planets) {
        if (_isPlanetInHouse(planet, house3) || _isPlanetInHouse(planet, house9)) {
          if (planet.name == '水星' || planet.name == '木星' || planet.name == '天王星') {
            sensitivity += 12.0;
          } else {
            sensitivity += 6.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析搬遷與環境變動敏感度
  double _analyzeRelocationSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets;

    // 4宮（家庭、居住）和9宮（遠行）
    if (houses.isNotEmpty && houses.length > 8) {
      final house4 = houses[3]; // 第4宮
      final house9 = houses[8]; // 第9宮

      for (final planet in planets) {
        if (_isPlanetInHouse(planet, house4) || _isPlanetInHouse(planet, house9)) {
          if (planet.name == '天王星' || planet.name == '木星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析心靈與命運轉折敏感度
  double _analyzeSpiritualTransformationSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets;

    // 8宮（轉化）和12宮（靈性）
    if (houses.isNotEmpty && houses.length > 11) {
      final house8 = houses[7];  // 第8宮
      final house12 = houses[11]; // 第12宮

      for (final planet in planets) {
        if (_isPlanetInHouse(planet, house8) || _isPlanetInHouse(planet, house12)) {
          if (planet.name == '冥王星' || planet.name == '海王星' || planet.name == '土星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 計算整體敏感度
  double _calculateOverallSensitivity(Map<String, dynamic> analysis) {
    double total = 0.0;
    int count = 0;

    for (final key in analysis.keys) {
      if (key.endsWith('_sensitivity') && analysis[key] is double) {
        total += analysis[key] as double;
        count++;
      }
    }

    return count > 0 ? total / count : 50.0;
  }

  /// 分析推運盤的時間觸發
  Future<Map<String, dynamic>> _analyzeProgressionChartsForTiming(
    Map<ChartType, ChartData> chartDataMap,
    BirthData birthData,
    DateTime date,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final analysis = <String, dynamic>{};

    try {
      // 行運分析
      if (chartDataMap.containsKey(ChartType.transit)) {
        analysis['transit_triggers'] = await _analyzeTransitTriggers(
          chartDataMap[ChartType.natal]!,
          chartDataMap[ChartType.transit]!,
          natalAnalysis,
        );
      }

      // 次限推運分析
      if (chartDataMap.containsKey(ChartType.secondaryProgression)) {
        analysis['progression_triggers'] = await _analyzeProgressionTriggers(
          chartDataMap[ChartType.natal]!,
          chartDataMap[ChartType.secondaryProgression]!,
          natalAnalysis,
        );
      }

      // 太陽弧推運分析
      if (chartDataMap.containsKey(ChartType.solarArcDirection)) {
        analysis['solar_arc_triggers'] = await _analyzeSolarArcTriggers(
          chartDataMap[ChartType.natal]!,
          chartDataMap[ChartType.solarArcDirection]!,
          natalAnalysis,
        );
      }

      logger.d('推運分析完成');

    } catch (e) {
      logger.e('推運分析失敗: $e');
    }

    return analysis;
  }

  /// 分析行運觸發
  Future<List<Map<String, dynamic>>> _analyzeTransitTriggers(
    ChartData natalChart,
    ChartData transitChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];

    // 檢查行運行星與本命行星的相位
    for (final transitPlanet in transitChart.planets) {
      for (final natalPlanet in natalChart.planets) {
        final orb = _calculateOrb(transitPlanet.longitude, natalPlanet.longitude);

        if (orb <= 3.0) { // 3度容許度
          final aspectType = _determineAspectType(transitPlanet.longitude, natalPlanet.longitude);

          if (_isSignificantAspect(aspectType)) {
            final trigger = {
              'type': 'transit_aspect',
              'transit_planet': transitPlanet.name,
              'natal_planet': natalPlanet.name,
              'aspect': aspectType,
              'orb': orb,
              'strength': _calculateAspectStrength(aspectType, orb),
              'event_potential': _calculateEventPotential(
                transitPlanet.name,
                natalPlanet.name,
                aspectType,
                natalAnalysis,
              ),
            };
            triggers.add(trigger);
          }
        }
      }
    }

    return triggers;
  }

  /// 分析推運觸發
  Future<List<Map<String, dynamic>>> _analyzeProgressionTriggers(
    ChartData natalChart,
    ChartData progressionChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];

    // 檢查推運行星與本命行星的相位
    for (final progPlanet in progressionChart.planets) {
      for (final natalPlanet in natalChart.planets) {
        final orb = _calculateOrb(progPlanet.longitude, natalPlanet.longitude);

        if (orb <= 1.0) { // 推運使用更嚴格的容許度
          final aspectType = _determineAspectType(progPlanet.longitude, natalPlanet.longitude);

          if (_isSignificantAspect(aspectType)) {
            final trigger = {
              'type': 'progression_aspect',
              'progression_planet': progPlanet.name,
              'natal_planet': natalPlanet.name,
              'aspect': aspectType,
              'orb': orb,
              'strength': _calculateAspectStrength(aspectType, orb),
              'event_potential': _calculateEventPotential(
                progPlanet.name,
                natalPlanet.name,
                aspectType,
                natalAnalysis,
              ),
            };
            triggers.add(trigger);
          }
        }
      }
    }

    return triggers;
  }

  /// 分析太陽弧推運觸發
  Future<List<Map<String, dynamic>>> _analyzeSolarArcTriggers(
    ChartData natalChart,
    ChartData solarArcChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];

    // 檢查太陽弧推運行星與本命行星的相位
    for (final solarArcPlanet in solarArcChart.planets) {
      for (final natalPlanet in natalChart.planets) {
        final orb = _calculateOrb(solarArcPlanet.longitude, natalPlanet.longitude);

        if (orb <= 1.0) { // 太陽弧推運使用嚴格的容許度
          final aspectType = _determineAspectType(solarArcPlanet.longitude, natalPlanet.longitude);

          if (_isSignificantAspect(aspectType)) {
            final trigger = {
              'type': 'solar_arc_aspect',
              'solar_arc_planet': solarArcPlanet.name,
              'natal_planet': natalPlanet.name,
              'aspect': aspectType,
              'orb': orb,
              'strength': _calculateAspectStrength(aspectType, orb),
              'event_potential': _calculateEventPotential(
                solarArcPlanet.name,
                natalPlanet.name,
                aspectType,
                natalAnalysis,
              ),
            };
            triggers.add(trigger);
          }
        }
      }
    }

    return triggers;
  }

  /// 綜合分析生成事件
  Future<List<AstroEvent>> _synthesizeEventsFromAnalysis(
    Map<ChartType, ChartData> chartDataMap,
    Map<String, dynamic> natalAnalysis,
    Map<String, dynamic> progressionAnalysis,
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 處理行運觸發
      if (progressionAnalysis.containsKey('transit_triggers')) {
        final transitTriggers = progressionAnalysis['transit_triggers'] as List<Map<String, dynamic>>;
        for (final trigger in transitTriggers) {
          final event = await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

      // 處理推運觸發
      if (progressionAnalysis.containsKey('progression_triggers')) {
        final progressionTriggers = progressionAnalysis['progression_triggers'] as List<Map<String, dynamic>>;
        for (final trigger in progressionTriggers) {
          final event = await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

      // 處理太陽弧推運觸發
      if (progressionAnalysis.containsKey('solar_arc_triggers')) {
        final solarArcTriggers = progressionAnalysis['solar_arc_triggers'] as List<Map<String, dynamic>>;
        for (final trigger in solarArcTriggers) {
          final event = await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

    } catch (e) {
      logger.e('綜合分析生成事件失敗: $e');
    }

    return events;
  }

  /// 從觸發器創建事件
  Future<AstroEvent?> _createEventFromTrigger(
    Map<String, dynamic> trigger,
    DateTime date,
    Map<String, dynamic> natalAnalysis,
  ) async {
    try {
      final eventPotential = trigger['event_potential'] as Map<String, dynamic>;
      final eventType = eventPotential['primary_event_type'] as String;
      final score = eventPotential['score'] as double;

      if (score < config.minimumEventScore) return null;

      final title = _generateEventTitle(trigger, eventType);
      final description = _generateEventDescription(trigger, eventType, natalAnalysis);

      return AstroEvent(
        id: 'enhanced_${date.millisecondsSinceEpoch}_${trigger.hashCode}',
        title: title,
        description: description,
        dateTime: date,
        type: _mapEventTypeToAstroEventType(eventType),
        color: _getEventTypeColor(eventType),
        icon: _getEventTypeIcon(eventType),
        importance: _calculateImportanceLevel(score),
        isVisible: true,
        score: score,
        eventImportance: EventImportance.fromScore(score),
        involvedPlanets: _extractInvolvedPlanets(trigger),
        aspectType: trigger['aspect'] as String?,
        orb: trigger['orb'] as double?,
        isExact: (trigger['orb'] as double?) != null && (trigger['orb'] as double) < 0.5,
        additionalData: {
          'trigger_data': trigger,
          'event_category': eventType,
          'analysis_source': 'enhanced_detection',
        },
      );
    } catch (e) {
      logger.e('創建事件失敗: $e');
      return null;
    }
  }

  // 輔助方法
  bool _isPlanetInHouse(dynamic planet, dynamic house) {
    // 簡化實作，實際需要根據具體的資料結構來判斷
    return false; // 待實作
  }

  bool _isRelationshipPlanet(String planetName) {
    return ['金星', '月亮', '火星'].contains(planetName);
  }

  bool _isCareerPlanet(String planetName) {
    return ['太陽', '土星', '木星'].contains(planetName);
  }

  bool _isChallengingAspect(String aspectType) {
    return ['刑', '沖', '半刑', '八分相'].contains(aspectType);
  }

  bool _isHarmoniousAspect(String aspectType) {
    return ['合', '拱', '六分相'].contains(aspectType);
  }

  double _calculateOrb(double longitude1, double longitude2) {
    double diff = (longitude1 - longitude2).abs();
    if (diff > 180) diff = 360 - diff;
    return diff;
  }

  String _determineAspectType(double longitude1, double longitude2) {
    final orb = _calculateOrb(longitude1, longitude2);

    if (orb <= 8) return '合';
    if ((orb - 60).abs() <= 6) return '六分相';
    if ((orb - 90).abs() <= 8) return '刑';
    if ((orb - 120).abs() <= 8) return '拱';
    if ((orb - 180).abs() <= 8) return '沖';

    return '無相位';
  }

  bool _isSignificantAspect(String aspectType) {
    return aspectType != '無相位';
  }

  double _calculateAspectStrength(String aspectType, double orb) {
    final baseStrength = {
      '合': 100.0,
      '沖': 90.0,
      '刑': 80.0,
      '拱': 70.0,
      '六分相': 60.0,
    }[aspectType] ?? 0.0;

    // 根據容許度調整強度
    final orbFactor = (8.0 - orb) / 8.0;
    return baseStrength * orbFactor;
  }

  Map<String, dynamic> _calculateEventPotential(
    String planet1,
    String planet2,
    String aspectType,
    Map<String, dynamic> natalAnalysis,
  ) {
    // 根據行星組合和相位類型判斷事件類型和強度
    final eventTypes = <String, double>{};

    // 感情事件
    if (_isRelationshipPlanet(planet1) || _isRelationshipPlanet(planet2)) {
      final sensitivity = natalAnalysis['relationship_sensitivity'] as double? ?? 50.0;
      eventTypes['relationship'] = sensitivity * 0.8;
    }

    // 事業事件
    if (_isCareerPlanet(planet1) || _isCareerPlanet(planet2)) {
      final sensitivity = natalAnalysis['career_sensitivity'] as double? ?? 50.0;
      eventTypes['career'] = sensitivity * 0.8;
    }

    // 找出最高分的事件類型
    String primaryEventType = 'general';
    double maxScore = 0.0;

    for (final entry in eventTypes.entries) {
      if (entry.value > maxScore) {
        maxScore = entry.value;
        primaryEventType = entry.key;
      }
    }

    return {
      'primary_event_type': primaryEventType,
      'score': maxScore,
      'all_potentials': eventTypes,
    };
  }

  String _generateEventTitle(Map<String, dynamic> trigger, String eventType) {
    final planet1 = trigger['transit_planet'] ?? trigger['progression_planet'] ?? trigger['solar_arc_planet'];
    final planet2 = trigger['natal_planet'];
    final aspect = trigger['aspect'];

    final eventTypeNames = {
      'relationship': '感情變化',
      'career': '事業轉折',
      'financial': '財務變動',
      'health': '健康狀況',
      'learning': '學習成長',
      'relocation': '環境變動',
      'spiritual': '心靈轉化',
    };

    final eventTypeName = eventTypeNames[eventType] ?? '重要事件';
    return '$eventTypeName - $planet1 $aspect $planet2';
  }

  String _generateEventDescription(
    Map<String, dynamic> trigger,
    String eventType,
    Map<String, dynamic> natalAnalysis,
  ) {
    final planet1 = trigger['transit_planet'] ?? trigger['progression_planet'] ?? trigger['solar_arc_planet'];
    final planet2 = trigger['natal_planet'];
    final aspect = trigger['aspect'];
    final strength = trigger['strength'] as double;

    final descriptions = {
      'relationship': '這個相位可能帶來感情關係的重要變化，需要特別關注伴侶關係和人際互動。',
      'career': '事業發展可能面臨重要轉折點，是時候重新評估職業方向和目標。',
      'financial': '財務狀況可能出現變化，建議謹慎處理投資和理財決策。',
      'health': '需要特別關注身體健康，建議進行健康檢查或調整生活習慣。',
      'learning': '這是學習新知識或技能的好時機，有助於個人成長和發展。',
      'relocation': '可能面臨搬遷或環境變化的機會，需要仔細考慮相關決定。',
      'spiritual': '內在心靈可能經歷重要轉化，是深度自我反省和成長的時期。',
    };

    final baseDescription = descriptions[eventType] ?? '這是一個重要的占星事件，值得關注。';
    return '$baseDescription\n\n相位強度：${strength.toStringAsFixed(1)}';
  }

  AstroEventType _mapEventTypeToAstroEventType(String eventType) {
    switch (eventType) {
      case 'relationship':
        return AstroEventType.transitAspect;
      case 'career':
        return AstroEventType.transitAspect;
      default:
        return AstroEventType.transitAspect;
    }
  }

  Color _getEventTypeColor(String eventType) {
    switch (eventType) {
      case 'relationship':
        return Colors.pink;
      case 'career':
        return Colors.blue;
      case 'financial':
        return Colors.green;
      case 'health':
        return Colors.red;
      case 'learning':
        return Colors.purple;
      case 'relocation':
        return Colors.orange;
      case 'spiritual':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  IconData _getEventTypeIcon(String eventType) {
    switch (eventType) {
      case 'relationship':
        return Icons.favorite;
      case 'career':
        return Icons.work;
      case 'financial':
        return Icons.attach_money;
      case 'health':
        return Icons.health_and_safety;
      case 'learning':
        return Icons.school;
      case 'relocation':
        return Icons.home;
      case 'spiritual':
        return Icons.self_improvement;
      default:
        return Icons.star;
    }
  }

  int _calculateImportanceLevel(double score) {
    if (score >= 80) return 5;
    if (score >= 60) return 4;
    if (score >= 40) return 3;
    if (score >= 20) return 2;
    return 1;
  }

  List<String> _extractInvolvedPlanets(Map<String, dynamic> trigger) {
    final planets = <String>[];

    if (trigger.containsKey('transit_planet')) {
      planets.add(trigger['transit_planet'] as String);
    }
    if (trigger.containsKey('progression_planet')) {
      planets.add(trigger['progression_planet'] as String);
    }
    if (trigger.containsKey('solar_arc_planet')) {
      planets.add(trigger['solar_arc_planet'] as String);
    }
    if (trigger.containsKey('natal_planet')) {
      planets.add(trigger['natal_planet'] as String);
    }

    return planets;
  }

  /// 偵測行運事件
  Future<List<AstroEvent>> _detectTransitEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算本命盤行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      // 計算行運盤行星位置
      final transitPlanets = await AstrologyService.calculatePlanetPositions(
        date,
        birthData.latitude,
        birthData.longitude,
      );
      
      final events = <AstroEvent>[];

      // 簡化的行運相位分析（實際實作需要更複雜的相位計算）
      // 這裡提供基本框架，實際使用時需要完整的相位計算邏輯
      for (final transitPlanet in transitPlanets) {
        for (final natalPlanet in natalPlanets) {
          // 簡化的相位檢查（實際需要使用 AspectCalculator）
          final angleDiff = (transitPlanet.longitude - natalPlanet.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          // 檢查主要相位（合相、對沖、四分相、三分相）
          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 8) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 8) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 6) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 6) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 3.0) {
            // 創建模擬的 AspectInfo 用於評分
            final mockAspect = {
              'planet1': {'name': transitPlanet.name},
              'planet2': {'name': natalPlanet.name},
              'aspect': aspectType,
              'orb': orb,
            };

            final event = AstroEvent(
              id: _generateEventId('transit', mockAspect, date),
              title: '${transitPlanet.name}${aspectType}${natalPlanet.name}',
              description: '行運${transitPlanet.name}與本命${natalPlanet.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: 3, // 預設重要性
              isVisible: true,
              color: AstroEventType.transitAspect.defaultColor,
              icon: AstroEventType.transitAspect.icon,
              score: 50.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(50.0),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [1, 1], // 預設宮位
              involvedSigns: [transitPlanet.sign, natalPlanet.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 1.0,
              additionalData: {
                'transitPlanet': transitPlanet,
                'natalPlanet': natalPlanet,
                'isTransit': true,
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測行運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測推運事件
  Future<List<AstroEvent>> _detectProgressionEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算推運日期（次限推運：一天等於一年）
      final daysSinceBirth = date.difference(birthData.dateTime).inDays;
      final progressionDate = birthData.dateTime.add(Duration(days: daysSinceBirth ~/ 365));

      // 計算推運行星位置
      final progressionPlanets = await AstrologyService.calculatePlanetPositions(
        progressionDate,
        birthData.latitude,
        birthData.longitude,
      );

      // 計算本命行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      final events = <AstroEvent>[];

      // 簡化的推運相位分析
      for (final progressionPlanet in progressionPlanets) {
        for (final natalPlanet in natalPlanets) {
          final angleDiff = (progressionPlanet.longitude - natalPlanet.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 1) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 1) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 1) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 1) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 1.0) {
            final mockAspect = {
              'planet1': {'name': progressionPlanet.name},
              'planet2': {'name': natalPlanet.name},
              'aspect': aspectType,
              'orb': orb,
            };

            final event = AstroEvent(
              id: _generateEventId('progression', mockAspect, date),
              title: '推運${progressionPlanet.name}${aspectType}${natalPlanet.name}',
              description: '推運${progressionPlanet.name}與本命${natalPlanet.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.progressionAspect,
              importance: 4, // 推運事件通常較重要
              isVisible: true,
              color: AstroEventType.progressionAspect.defaultColor,
              icon: AstroEventType.progressionAspect.icon,
              score: 60.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(60.0),
              involvedPlanets: [progressionPlanet.name, natalPlanet.name],
              involvedHouses: [1, 1], // 預設宮位
              involvedSigns: [progressionPlanet.sign, natalPlanet.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 0.5,
              additionalData: {
                'progressionPlanet': progressionPlanet,
                'natalPlanet': natalPlanet,
                'isProgression': true,
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測推運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測太陽弧推運事件
  Future<List<AstroEvent>> _detectSolarArcEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算太陽弧推運（太陽的移動度數應用到所有行星）
      final yearsSinceBirth = date.difference(birthData.dateTime).inDays / 365.25;
      final solarArcDegrees = yearsSinceBirth; // 簡化：每年約1度

      // 計算本命行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      final events = <AstroEvent>[];

      // 簡化的太陽弧推運相位分析
      for (final natalPlanet1 in natalPlanets) {
        for (final natalPlanet2 in natalPlanets) {
          if (natalPlanet1.name == natalPlanet2.name) continue;

          // 計算太陽弧推運後的位置
          final solarArcLongitude = (natalPlanet1.longitude + solarArcDegrees) % 360;
          final angleDiff = (solarArcLongitude - natalPlanet2.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 1) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 1) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 1) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 1) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 1.0) {
            final mockAspect = {
              'planet1': {'name': natalPlanet1.name},
              'planet2': {'name': natalPlanet2.name},
              'aspect': aspectType,
              'orb': orb,
            };

            final event = AstroEvent(
              id: _generateEventId('solarArc', mockAspect, date),
              title: '太陽弧${natalPlanet1.name}${aspectType}${natalPlanet2.name}',
              description: '太陽弧推運${natalPlanet1.name}與本命${natalPlanet2.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.solarArcAspect,
              importance: 4, // 太陽弧推運事件較重要
              isVisible: true,
              color: AstroEventType.solarArcAspect.defaultColor,
              icon: AstroEventType.solarArcAspect.icon,
              score: 65.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(65.0),
              involvedPlanets: [natalPlanet1.name, natalPlanet2.name],
              involvedHouses: [1, 1], // 預設宮位
              involvedSigns: [natalPlanet1.sign, natalPlanet2.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 0.5,
              additionalData: {
                'solarArcPlanet': natalPlanet1,
                'natalPlanet': natalPlanet2,
                'solarArcDegrees': solarArcDegrees,
                'isSolarArc': true,
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測太陽弧推運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測行星換座事件
  Future<List<AstroEvent>> _detectSignChangeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    // TODO: 實作行星換座偵測邏輯
    return [];
  }

  /// 偵測行星換宮事件
  Future<List<AstroEvent>> _detectHouseChangeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    // TODO: 實作行星換宮偵測邏輯
    return [];
  }

  /// 檢查相位是否在指定日期活躍
  bool _isAspectActiveOnDate(dynamic aspect, DateTime date) {
    // 簡化實作：檢查容許度是否在合理範圍內
    if (aspect is Map) {
      return (aspect['orb'] as double) <= 3.0;
    }
    return false;
  }

  /// 生成事件ID
  String _generateEventId(String type, dynamic aspect, DateTime date) {
    final dateStr = date.toIso8601String().substring(0, 10);
    String aspectStr;

    if (aspect is Map) {
      final planet1Name = aspect['planet1']['name'] as String;
      final planet2Name = aspect['planet2']['name'] as String;
      final aspectType = aspect['aspect'] as String;
      aspectStr = '${planet1Name}_${aspectType}_${planet2Name}';
    } else {
      aspectStr = 'unknown_aspect';
    }

    return '${type}_${dateStr}_${aspectStr}';
  }

  /// 分數轉換為重要性等級
  int _scoreToImportance(double score) {
    if (score >= 90) return 5;
    if (score >= 70) return 4;
    if (score >= 50) return 3;
    if (score >= 30) return 2;
    return 1;
  }

  /// 獲取相位顏色
  Color _getAspectColor(String aspectName) {
    switch (aspectName) {
      case '合相':
        return Colors.red;
      case '對沖':
        return Colors.blue;
      case '四分相':
        return Colors.orange;
      case '三分相':
        return Colors.green;
      case '六分相':
        return Colors.purple;
      default:
        return AstroEventType.transitAspect.defaultColor;
    }
  }

  /// 偵測人生重大事件
  Future<List<AstroEvent>> _detectMajorLifeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    return await LifeEventsDetector.detectMajorLifeEvents(birthData, date);
  }
}
