import 'package:flutter/material.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/astrology/event_detection_config.dart';
import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/event_timeline_data.dart';
import '../../../data/models/user/birth_data.dart';
import '../astrology_service.dart';
import '../calculations/event_score_calculator.dart';
import 'event_cache_service.dart';
import 'life_events_detector.dart';

/// 事件偵測服務
/// 
/// 負責偵測和分析占星事件，包括：
/// - 行運事件偵測
/// - 推運事件偵測
/// - 事件評分計算
/// - 時間軸資料生成
class EventDetectionService {
  /// 事件偵測配置
  final EventDetectionConfig config;
  
  /// 事件評分計算器
  late final EventScoreCalculator _scoreCalculator;
  
  /// 占星服務
  final AstrologyService _astrologyService;

  EventDetectionService({
    required this.config,
    required AstrologyService astrologyService,
  }) : _astrologyService = astrologyService {
    _scoreCalculator = EventScoreCalculator(
      config: EventScoreConfig.defaultConfig(),
    );
  }

  /// 偵測指定時間範圍內的所有事件
  ///
  /// [birthData] 出生資料
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// [useCache] 是否使用快取
  ///
  /// 返回事件時間軸資料
  Future<EventTimelineData> detectEvents(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate, {
    bool useCache = true,
  }) async {
    logger.d('開始偵測事件: ${startDate.toString()} 到 ${endDate.toString()}');

    // 嘗試從快取載入
    if (useCache) {
      final cachedData = await EventCacheService.getCachedEventData(
        birthData,
        startDate,
        endDate,
      );

      if (cachedData != null) {
        logger.d('使用快取資料');
        return cachedData;
      }
    }

    final dailyScores = <DailyEventScore>[];
    final allEvents = <AstroEvent>[];

    // 逐日偵測事件
    DateTime currentDate = startDate;
    while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
      try {
        final dayEvents = await _detectDailyEvents(birthData, currentDate);
        final dayScores = dayEvents.map((event) =>
          _scoreCalculator.calculateAspectScore(
            event.additionalData?['aspect'],
            birthData,
            isTransit: event.type == AstroEventType.transitAspect,
            isPrecise: event.isExact ?? false,
          )
        ).toList();

        final dailyScore = DailyEventScore.fromEvents(currentDate, dayScores);
        dailyScores.add(dailyScore);
        allEvents.addAll(dayEvents);

        logger.d('${currentDate.toString().substring(0, 10)}: ${dayEvents.length} 個事件, 總分: ${dailyScore.totalScore.toStringAsFixed(1)}');
      } catch (e) {
        logger.e('偵測 $currentDate 事件時出錯: $e');
        // 添加空的每日評分以保持連續性
        dailyScores.add(DailyEventScore(
          date: currentDate,
          totalScore: 0,
          events: [],
          eventCount: 0,
        ));
      }

      currentDate = currentDate.add(const Duration(days: 1));
    }

    final timelineData = EventTimelineData.fromDailyScores(
      startDate,
      endDate,
      dailyScores,
    );

    // 快取結果
    if (useCache) {
      await EventCacheService.cacheEventData(
        birthData,
        startDate,
        endDate,
        timelineData,
      );
    }

    logger.d('事件偵測完成: 總共 ${allEvents.length} 個事件');
    return timelineData;
  }

  /// 偵測單日事件
  Future<List<AstroEvent>> _detectDailyEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];
    
    // 1. 偵測行運事件
    if (config.enabledEventTypes.contains(AstroEventType.transitAspect)) {
      final transitEvents = await _detectTransitEvents(birthData, date);
      events.addAll(transitEvents);
    }
    
    // 2. 偵測推運事件
    if (config.enabledEventTypes.contains(AstroEventType.progressionAspect)) {
      final progressionEvents = await _detectProgressionEvents(birthData, date);
      events.addAll(progressionEvents);
    }
    
    // 3. 偵測太陽弧推運事件
    if (config.enabledEventTypes.contains(AstroEventType.solarArcAspect)) {
      final solarArcEvents = await _detectSolarArcEvents(birthData, date);
      events.addAll(solarArcEvents);
    }
    
    // 4. 偵測行星換座事件
    if (config.enabledEventTypes.contains(AstroEventType.planetSignChange)) {
      final signChangeEvents = await _detectSignChangeEvents(birthData, date);
      events.addAll(signChangeEvents);
    }
    
    // 5. 偵測行星換宮事件
    if (config.enabledEventTypes.contains(AstroEventType.planetHouseChange)) {
      final houseChangeEvents = await _detectHouseChangeEvents(birthData, date);
      events.addAll(houseChangeEvents);
    }

    // 6. 偵測人生重大事件
    final majorLifeEvents = await _detectMajorLifeEvents(birthData, date);
    events.addAll(majorLifeEvents);

    // 過濾低分事件
    return events.where((event) =>
      (event.score ?? 0) >= config.minimumEventScore
    ).toList();
  }

  /// 偵測行運事件
  Future<List<AstroEvent>> _detectTransitEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算本命盤行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      // 計算行運盤行星位置
      final transitPlanets = await AstrologyService.calculatePlanetPositions(
        date,
        birthData.latitude,
        birthData.longitude,
      );
      
      final events = <AstroEvent>[];

      // 簡化的行運相位分析（實際實作需要更複雜的相位計算）
      // 這裡提供基本框架，實際使用時需要完整的相位計算邏輯
      for (final transitPlanet in transitPlanets) {
        for (final natalPlanet in natalPlanets) {
          // 簡化的相位檢查（實際需要使用 AspectCalculator）
          final angleDiff = (transitPlanet.longitude - natalPlanet.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          // 檢查主要相位（合相、對沖、四分相、三分相）
          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 8) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 8) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 6) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 6) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 3.0) {
            // 創建模擬的 AspectInfo 用於評分
            final mockAspect = {
              'planet1': {'name': transitPlanet.name},
              'planet2': {'name': natalPlanet.name},
              'aspect': aspectType,
              'orb': orb,
            };

            final event = AstroEvent(
              id: _generateEventId('transit', mockAspect, date),
              title: '${transitPlanet.name}${aspectType}${natalPlanet.name}',
              description: '行運${transitPlanet.name}與本命${natalPlanet.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: 3, // 預設重要性
              isVisible: true,
              color: AstroEventType.transitAspect.defaultColor,
              icon: AstroEventType.transitAspect.icon,
              score: 50.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(50.0),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [1, 1], // 預設宮位
              involvedSigns: [transitPlanet.sign, natalPlanet.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 1.0,
              additionalData: {
                'transitPlanet': transitPlanet,
                'natalPlanet': natalPlanet,
                'isTransit': true,
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測行運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測推運事件
  Future<List<AstroEvent>> _detectProgressionEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算推運日期（次限推運：一天等於一年）
      final daysSinceBirth = date.difference(birthData.dateTime).inDays;
      final progressionDate = birthData.dateTime.add(Duration(days: daysSinceBirth ~/ 365));

      // 計算推運行星位置
      final progressionPlanets = await AstrologyService.calculatePlanetPositions(
        progressionDate,
        birthData.latitude,
        birthData.longitude,
      );

      // 計算本命行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      final events = <AstroEvent>[];

      // 簡化的推運相位分析
      for (final progressionPlanet in progressionPlanets) {
        for (final natalPlanet in natalPlanets) {
          final angleDiff = (progressionPlanet.longitude - natalPlanet.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 1) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 1) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 1) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 1) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 1.0) {
            final mockAspect = {
              'planet1': {'name': progressionPlanet.name},
              'planet2': {'name': natalPlanet.name},
              'aspect': aspectType,
              'orb': orb,
            };

            final event = AstroEvent(
              id: _generateEventId('progression', mockAspect, date),
              title: '推運${progressionPlanet.name}${aspectType}${natalPlanet.name}',
              description: '推運${progressionPlanet.name}與本命${natalPlanet.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.progressionAspect,
              importance: 4, // 推運事件通常較重要
              isVisible: true,
              color: AstroEventType.progressionAspect.defaultColor,
              icon: AstroEventType.progressionAspect.icon,
              score: 60.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(60.0),
              involvedPlanets: [progressionPlanet.name, natalPlanet.name],
              involvedHouses: [1, 1], // 預設宮位
              involvedSigns: [progressionPlanet.sign, natalPlanet.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 0.5,
              additionalData: {
                'progressionPlanet': progressionPlanet,
                'natalPlanet': natalPlanet,
                'isProgression': true,
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測推運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測太陽弧推運事件
  Future<List<AstroEvent>> _detectSolarArcEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算太陽弧推運（太陽的移動度數應用到所有行星）
      final yearsSinceBirth = date.difference(birthData.dateTime).inDays / 365.25;
      final solarArcDegrees = yearsSinceBirth; // 簡化：每年約1度

      // 計算本命行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      final events = <AstroEvent>[];

      // 簡化的太陽弧推運相位分析
      for (final natalPlanet1 in natalPlanets) {
        for (final natalPlanet2 in natalPlanets) {
          if (natalPlanet1.name == natalPlanet2.name) continue;

          // 計算太陽弧推運後的位置
          final solarArcLongitude = (natalPlanet1.longitude + solarArcDegrees) % 360;
          final angleDiff = (solarArcLongitude - natalPlanet2.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 1) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 1) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 1) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 1) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 1.0) {
            final mockAspect = {
              'planet1': {'name': natalPlanet1.name},
              'planet2': {'name': natalPlanet2.name},
              'aspect': aspectType,
              'orb': orb,
            };

            final event = AstroEvent(
              id: _generateEventId('solarArc', mockAspect, date),
              title: '太陽弧${natalPlanet1.name}${aspectType}${natalPlanet2.name}',
              description: '太陽弧推運${natalPlanet1.name}與本命${natalPlanet2.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.solarArcAspect,
              importance: 4, // 太陽弧推運事件較重要
              isVisible: true,
              color: AstroEventType.solarArcAspect.defaultColor,
              icon: AstroEventType.solarArcAspect.icon,
              score: 65.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(65.0),
              involvedPlanets: [natalPlanet1.name, natalPlanet2.name],
              involvedHouses: [1, 1], // 預設宮位
              involvedSigns: [natalPlanet1.sign, natalPlanet2.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 0.5,
              additionalData: {
                'solarArcPlanet': natalPlanet1,
                'natalPlanet': natalPlanet2,
                'solarArcDegrees': solarArcDegrees,
                'isSolarArc': true,
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測太陽弧推運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測行星換座事件
  Future<List<AstroEvent>> _detectSignChangeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    // TODO: 實作行星換座偵測邏輯
    return [];
  }

  /// 偵測行星換宮事件
  Future<List<AstroEvent>> _detectHouseChangeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    // TODO: 實作行星換宮偵測邏輯
    return [];
  }

  /// 檢查相位是否在指定日期活躍
  bool _isAspectActiveOnDate(dynamic aspect, DateTime date) {
    // 簡化實作：檢查容許度是否在合理範圍內
    if (aspect is Map) {
      return (aspect['orb'] as double) <= 3.0;
    }
    return false;
  }

  /// 生成事件ID
  String _generateEventId(String type, dynamic aspect, DateTime date) {
    final dateStr = date.toIso8601String().substring(0, 10);
    String aspectStr;

    if (aspect is Map) {
      final planet1Name = aspect['planet1']['name'] as String;
      final planet2Name = aspect['planet2']['name'] as String;
      final aspectType = aspect['aspect'] as String;
      aspectStr = '${planet1Name}_${aspectType}_${planet2Name}';
    } else {
      aspectStr = 'unknown_aspect';
    }

    return '${type}_${dateStr}_${aspectStr}';
  }

  /// 分數轉換為重要性等級
  int _scoreToImportance(double score) {
    if (score >= 90) return 5;
    if (score >= 70) return 4;
    if (score >= 50) return 3;
    if (score >= 30) return 2;
    return 1;
  }

  /// 獲取相位顏色
  Color _getAspectColor(String aspectName) {
    switch (aspectName) {
      case '合相':
        return Colors.red;
      case '對沖':
        return Colors.blue;
      case '四分相':
        return Colors.orange;
      case '三分相':
        return Colors.green;
      case '六分相':
        return Colors.purple;
      default:
        return AstroEventType.transitAspect.defaultColor;
    }
  }

  /// 偵測人生重大事件
  Future<List<AstroEvent>> _detectMajorLifeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    return await LifeEventsDetector.detectMajorLifeEvents(birthData, date);
  }
}
