import 'package:flutter/material.dart';

import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/user/birth_data.dart';
import '../astrology_service.dart';

/// 人生重大事件偵測器
class MajorLifeEventsDetector {
  
  /// 偵測健康與身體狀況事件
  static Future<List<AstroEvent>> detectHealthEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];
    
    // 關鍵行星：火星、土星、天王星、冥王星
    final keyPlanets = ['Mars', 'Saturn', 'Uranus', 'Pluto'];
    // 關鍵宮位：6宮（健康）、8宮（醫療與康復）
    final keyHouses = [6, 8];
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        final isKeyHouse = keyHouses.contains(natalPlanet.house);
        if (!isKeyHouse) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateHealthEventScore(
            transitPlanet.name,
            natalPlanet.name,
            aspect['type'],
            natalPlanet.house,
          );
          
          if (score >= 70) {
            final event = AstroEvent(
              id: 'health_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getHealthEventTitle(transitPlanet.name, aspect['type'], natalPlanet.house),
              description: _getHealthEventDescription(transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getHealthEventColor(transitPlanet.name),
              icon: _getHealthEventIcon(natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '健康與身體狀況',
                'keyHouse': natalPlanet.house,
                'isHealthEvent': true,
              },
            );
            
            events.add(event);
          }
        }
      }
    }
    
    return events;
  }

  /// 偵測學習與成長事件
  static Future<List<AstroEvent>> detectEducationEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];
    
    // 關鍵行星：水星、木星、天王星
    final keyPlanets = ['Mercury', 'Jupiter', 'Uranus'];
    // 關鍵宮位：3宮（學習交流）、9宮（哲學與高等教育）
    final keyHouses = [3, 9];
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        final isKeyHouse = keyHouses.contains(natalPlanet.house);
        if (!isKeyHouse) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateEducationEventScore(
            transitPlanet.name,
            natalPlanet.name,
            aspect['type'],
            natalPlanet.house,
          );
          
          if (score >= 55) {
            final event = AstroEvent(
              id: 'education_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getEducationEventTitle(transitPlanet.name, aspect['type'], natalPlanet.house),
              description: _getEducationEventDescription(transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getEducationEventColor(transitPlanet.name),
              icon: _getEducationEventIcon(natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '學習與成長',
                'keyHouse': natalPlanet.house,
                'isEducationEvent': true,
              },
            );
            
            events.add(event);
          }
        }
      }
    }
    
    return events;
  }

  /// 偵測搬遷與環境變動事件
  static Future<List<AstroEvent>> detectRelocationEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];
    
    // 關鍵行星：天王星、木星
    final keyPlanets = ['Uranus', 'Jupiter'];
    // 關鍵宮位：4宮（家）、9宮（遠行）
    final keyHouses = [4, 9];
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        final isKeyHouse = keyHouses.contains(natalPlanet.house);
        if (!isKeyHouse) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 2.0) {
          final score = _calculateRelocationEventScore(
            transitPlanet.name,
            natalPlanet.name,
            aspect['type'],
            natalPlanet.house,
          );
          
          if (score >= 65) {
            final event = AstroEvent(
              id: 'relocation_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getRelocationEventTitle(transitPlanet.name, aspect['type'], natalPlanet.house),
              description: _getRelocationEventDescription(transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getRelocationEventColor(transitPlanet.name),
              icon: _getRelocationEventIcon(natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '搬遷與環境變動',
                'keyHouse': natalPlanet.house,
                'isRelocationEvent': true,
              },
            );
            
            events.add(event);
          }
        }
      }
    }
    
    return events;
  }

  /// 偵測心靈與命運轉折事件
  static Future<List<AstroEvent>> detectSpiritualEvents(
    List<dynamic> natalPlanets,
    List<dynamic> transitPlanets,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];
    
    // 關鍵行星：冥王星、海王星、土星
    final keyPlanets = ['Pluto', 'Neptune', 'Saturn'];
    // 關鍵宮位：8宮（死亡與重生）、12宮（潛意識）
    final keyHouses = [8, 12];
    
    for (final transitPlanet in transitPlanets) {
      if (!keyPlanets.contains(transitPlanet.name)) continue;
      
      for (final natalPlanet in natalPlanets) {
        final isKeyHouse = keyHouses.contains(natalPlanet.house);
        if (!isKeyHouse) continue;
        
        final aspect = _calculateAspect(transitPlanet.longitude, natalPlanet.longitude);
        if (aspect != null && aspect['orb'] <= 1.5) { // 更嚴格的容許度
          final score = _calculateSpiritualEventScore(
            transitPlanet.name,
            natalPlanet.name,
            aspect['type'],
            natalPlanet.house,
          );
          
          if (score >= 75) {
            final event = AstroEvent(
              id: 'spiritual_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
              title: _getSpiritualEventTitle(transitPlanet.name, aspect['type'], natalPlanet.house),
              description: _getSpiritualEventDescription(transitPlanet.name, natalPlanet.name, aspect['type'], natalPlanet.house),
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: _scoreToImportance(score),
              isVisible: true,
              color: _getSpiritualEventColor(transitPlanet.name),
              icon: _getSpiritualEventIcon(natalPlanet.house),
              score: score,
              eventImportance: EventImportanceExtension.fromScore(score),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house],
              aspectType: aspect['type'],
              orb: aspect['orb'],
              additionalData: {
                'category': '心靈與命運轉折',
                'keyHouse': natalPlanet.house,
                'isSpiritualEvent': true,
              },
            );
            
            events.add(event);
          }
        }
      }
    }
    
    return events;
  }
