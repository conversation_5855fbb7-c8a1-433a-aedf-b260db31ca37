# fl_chart 間隔值錯誤修正記錄

## 📋 問題概述

在使用 fl_chart 繪製時間軸圖表時，出現了 `horizontalInterval` 不能為零的斷言錯誤。這個問題發生在事件資料為空或所有事件分數為零的情況下。

## 🔍 錯誤分析

### 錯誤訊息
```
flutter: │ 'package:fl_chart/src/chart/base/axis_chart/axis_chart_data.dart': Failed assertion: line 562 pos 11: 'horizontalInterval != 0': FlGridData.horizontalInterval couldn't be zero
```

### 錯誤位置
- **檔案**：`lib/presentation/widgets/astrology/event_timeline_widget.dart`
- **方法**：`_buildChart()` 第 263 行
- **觸發條件**：當 `widget.timelineData.maxScore` 為 0 時

### 問題原因
1. **空資料情況**：當沒有事件資料時，`maxScore` 為 0
2. **零分事件**：當所有事件分數都為 0 時，`maxScore` 也為 0
3. **除法運算**：`maxScore / 5` 結果為 0，違反了 fl_chart 的約束條件

## 🔧 修正方案

### 1. 間隔值安全計算

**修正前**：
```dart
LineChart(
  LineChartData(
    gridData: FlGridData(
      horizontalInterval: widget.timelineData.maxScore / 5,
      verticalInterval: spots.length / 10,
      // ...
    ),
    // ...
  ),
)
```

**修正後**：
```dart
// 確保間隔值不為零
final maxScore = widget.timelineData.maxScore;
final horizontalInterval = maxScore > 0 ? maxScore / 5 : 10.0;
final verticalInterval = spots.length > 0 ? spots.length / 10 : 1.0;

LineChart(
  LineChartData(
    gridData: FlGridData(
      horizontalInterval: horizontalInterval,
      verticalInterval: verticalInterval,
      // ...
    ),
    // ...
  ),
)
```

### 2. 軸標題間隔修正

**修正前**：
```dart
leftTitles: AxisTitles(
  sideTitles: SideTitles(
    interval: widget.timelineData.maxScore / 5,
    // ...
  ),
),
bottomTitles: AxisTitles(
  sideTitles: SideTitles(
    interval: spots.length / 6,
    // ...
  ),
),
```

**修正後**：
```dart
leftTitles: AxisTitles(
  sideTitles: SideTitles(
    interval: horizontalInterval,
    // ...
  ),
),
bottomTitles: AxisTitles(
  sideTitles: SideTitles(
    interval: verticalInterval,
    // ...
  ),
),
```

### 3. 圖表範圍安全設定

**修正前**：
```dart
minX: 0,
maxX: spots.length.toDouble() - 1,
minY: 0,
maxY: widget.timelineData.maxScore * 1.1,
```

**修正後**：
```dart
minX: 0,
maxX: math.max(spots.length.toDouble() - 1, 1),
minY: 0,
maxY: math.max(maxScore * 1.1, 10),
```

### 4. 空資料處理

**修正前**：
```dart
List<FlSpot> _generateSpots() {
  final spots = <FlSpot>[];
  
  for (int i = 0; i < widget.timelineData.dailyScores.length; i++) {
    final score = widget.timelineData.dailyScores[i];
    spots.add(FlSpot(i.toDouble(), score.totalScore));
  }
  
  return spots;
}
```

**修正後**：
```dart
List<FlSpot> _generateSpots() {
  final spots = <FlSpot>[];
  
  // 如果沒有資料，返回預設點
  if (widget.timelineData.dailyScores.isEmpty) {
    spots.add(const FlSpot(0, 0));
    spots.add(const FlSpot(1, 0));
    return spots;
  }
  
  for (int i = 0; i < widget.timelineData.dailyScores.length; i++) {
    final score = widget.timelineData.dailyScores[i];
    spots.add(FlSpot(i.toDouble(), score.totalScore));
  }
  
  return spots;
}
```

## 📊 修正統計

| 修正項目 | 問題類型 | 修正方案 | 狀態 |
|---------|---------|---------|------|
| 網格間隔 | 零值錯誤 | 安全計算 + 預設值 | ✅ 完成 |
| 軸標題間隔 | 零值錯誤 | 使用統一變數 | ✅ 完成 |
| 圖表範圍 | 零值錯誤 | math.max 保護 | ✅ 完成 |
| 空資料處理 | 邊界情況 | 預設數據點 | ✅ 完成 |

## 🛡️ 安全機制

### 1. 零值保護
```dart
// 確保間隔值永遠不為零
final horizontalInterval = maxScore > 0 ? maxScore / 5 : 10.0;
final verticalInterval = spots.length > 0 ? spots.length / 10 : 1.0;
```

### 2. 最小值保證
```dart
// 確保圖表範圍有最小值
maxX: math.max(spots.length.toDouble() - 1, 1),
maxY: math.max(maxScore * 1.1, 10),
```

### 3. 空資料預設
```dart
// 空資料時提供預設數據點
if (widget.timelineData.dailyScores.isEmpty) {
  spots.add(const FlSpot(0, 0));
  spots.add(const FlSpot(1, 0));
  return spots;
}
```

## 🎯 預設值策略

### 間隔值預設
- **水平間隔**：10.0（當 maxScore 為 0 時）
- **垂直間隔**：1.0（當沒有數據點時）

### 圖表範圍預設
- **最大 X 值**：至少為 1
- **最大 Y 值**：至少為 10

### 數據點預設
- **最少數據點**：2 個點 (0,0) 和 (1,0)
- **確保圖表可繪製**：避免單點或空數據

## 🧪 測試場景

### 1. 空資料測試
```dart
// 測試完全沒有事件資料的情況
final emptyTimelineData = EventTimelineData(
  startDate: DateTime.now(),
  endDate: DateTime.now().add(Duration(days: 1)),
  dailyScores: [],
  // ...
);
```

### 2. 零分資料測試
```dart
// 測試所有事件分數都為零的情況
final zeroScoreData = EventTimelineData(
  // ... 所有 dailyScores 的 totalScore 都為 0
);
```

### 3. 單日資料測試
```dart
// 測試只有一天資料的情況
final singleDayData = EventTimelineData(
  dailyScores: [DailyEventScore(date: DateTime.now(), totalScore: 0)],
  // ...
);
```

## 🔮 預防措施

### 1. 資料驗證
```dart
// 在建立圖表前驗證資料
bool _isValidData() {
  return widget.timelineData.dailyScores.isNotEmpty &&
         widget.timelineData.maxScore >= 0;
}
```

### 2. 錯誤邊界
```dart
// 使用 try-catch 包裝圖表建立
Widget _buildChart() {
  try {
    // 圖表建立邏輯
    return LineChart(...);
  } catch (e) {
    return _buildErrorWidget();
  }
}
```

### 3. 載入狀態
```dart
// 在資料載入完成前顯示載入指示器
if (widget.timelineData.dailyScores.isEmpty) {
  return const CircularProgressIndicator();
}
```

## 📝 最佳實踐

### 1. fl_chart 使用原則
- **永遠檢查間隔值**：確保不為零
- **提供預設範圍**：避免極端值
- **處理空資料**：提供合理的預設數據

### 2. 數據處理原則
- **驗證輸入資料**：在使用前檢查資料有效性
- **提供預設值**：為邊界情況提供合理預設
- **優雅降級**：在錯誤情況下提供替代顯示

### 3. 錯誤處理原則
- **預防勝於治療**：在問題發生前預防
- **提供回饋**：讓用戶知道發生了什麼
- **保持功能性**：即使在錯誤情況下也要保持基本功能

---

*fl_chart 間隔值錯誤已修正完成，圖表現在可以安全處理各種邊界情況，包括空資料、零分資料和單日資料。*
